import React, { useState } from "react";
import products from "../../data/products";

const Cart = () => {
  const [quantity, setQuantity] = useState(1);
  // Sửa hàm totalPrice để sử dụng quantity từ state
  const totalPrice = (product) => (product.price || 0) * quantity;
  const handleQuantityChange = (change) => {
    setQuantity((prev) => Math.max(1, prev + change));
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-24 pb-12 ">
      <div className="max-w-6xl mx-auto px-6 lg:px-8">
        <div className="cart-section">
          <p className="font-bold text-center">GIỎ HÀNG</p>
          <hr className="mb-6" />
          <table className="w-full table-auto  ">
            <thead>
              <th className="px-4 py-2"><PERSON><PERSON><PERSON></th>
              <th className="px-4 py-2"><PERSON><PERSON><PERSON> phẩm</th>
              <th className="px-4 py-2"><PERSON><PERSON><PERSON></th>
              <th className="px-4 py-2"><PERSON><PERSON> lượng</th>
              <th className="px-4 py-2">Tổng</th>
            </thead>
            <tbody>
              {products.length > 0 ? (
                products.map((product, index) => {
                  return (
                    <tr key={index}>
                      <td className="px-4 py-2">
                        <img
                          src={product.mainImage || "default-image.jpg"}
                          alt={product.name || "Sản phẩm"}
                          className="w-16 h-16 object-cover"
                        />
                      </td>
                      <td className="px-4 py-2">
                        <p>{product.name || "Tên sản phẩm"}</p>
                        <p>{product.category || "Danh mục"}</p>
                        <p>
                          {product.colors || "Không có màu"},{" "}
                          {product.sizes || "Không có kích thước"}
                        </p>
                      </td>
                      <td className="px-4 py-2">
                        <p>{(product.price || 0).toLocaleString("vi-VN")} ₫</p>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => handleQuantityChange(-1)}
                            className="w-10 h-10 rounded-full"
                          >
                            -
                          </button>
                          <p>{quantity}</p>
                          <button
                            onClick={() => handleQuantityChange(1)}
                            className="w-10 h-10 rounded-full"
                          >
                            +
                          </button>
                        </div>
                      </td>
                      <td className="px-4 py-2">
                        {totalPrice(product).toLocaleString("vi-VN")} ₫
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="5" className="text-center py-4">
                    Giỏ hàng trống
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          <hr className="mb-6" />
        </div>
      </div>
    </div>
  );
};

export default Cart;
